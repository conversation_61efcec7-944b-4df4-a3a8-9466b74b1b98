---
OPERATOR_TYPE: year
QUEUE_LISTEN: to_filter_year_queue
LISTEN_EXCHANGE: from_preprocessor
END_EXCHANGE: filter_year_end_exchange # cluster
SEND_EXCHANGE: from_filter_year
ROUTES: #clave: valor | de donde viene : a donde va
  filter_by_year_transaction_items:
    - groupBy_ymp
  filter_by_year_transactions:    
    - groupBy_ymus
    - filter_by_time
  cluster-year:
    - cluster-year
---
OPERATOR_TYPE: time
QUEUE_LISTEN: from_filter_year_queue
LISTEN_EXCHANGE: from_filter_year
END_EXCHANGE: filter_time_end_exchange
SEND_EXCHANGE: from_filter_time
ROUTES:
  filter_by_time:
    - filter_by_amount
    - groupBy_yss
  cluster-time:
    - cluster-time
---
OPERATOR_TYPE: amount
QUEUE_LISTEN: from_filter_time_queue
LISTEN_EXCHANGE: from_filter_time
END_EXCHANGE: filter_amount_end_exchange
SEND_EXCHANGE: results_exchange
ROUTES:
  filter_by_amount:
    - results_to_gateway #final result
  cluster-amount:
    - cluster-amount