# Trabajo Práctico Sistemas Distribuidos

## Grupo 20 - Integrantes:

- **Integrante 1** - [<PERSON><PERSON>](https://github.com/emiliaduzac) - Padron: 110307
- **Integrante 2** - [Germinario Agustina](https://github.com/agus-germi) - Padron: 109095
- **Integrante 3** - [<PERSON><PERSON><PERSON>](https://github.com/Brubrux) - Padron: 109713

## TP-Coffee Shop Analysis

Se solicita un sistema distribuido que analice la información de ventas en una cadena de negocios de Cafés en Malasia.
- Se cuenta con información transaccional por ventas (montos, items vendidos, etc), información de los clientes, de las tiendas y de los productos ofrecidos.
- Se debe obtener:
    1. Transacciones (Id y monto) realizadas durante 2024 y 2025 entre las 06:00 AM y las 11:00 PM con monto total mayor o igual a 75.
    2. Productos más vendidos (nombre y cant) y productos que más ganancias han generado (nombre y monto), para cada mes en 2024 y 2025.
    3. TPV (Total Payment Value) por cada semestre en 2024 y 2025, para cada sucursal, para transacciones realizadas entre las 06:00 AM y las 11:00 PM.
    4. Fecha de cumpleaños de los 3 clientes que han hecho más compras durante 2024 y 2025, para cada sucursal.
---
## Cómo levantar los servicios con Docker Compose

1. **Construir las imágenes (solo la primera vez o si hay cambios en el código/Dockerfile):**
   ```sh
   docker-compose -f docker-compose-dev.yml build
   ```
   a. Si se quiere evitar el cache durante la construcción:
   ```sh
   docker-compose -f docker-compose-dev.yml build --no-cache
    ```
2. **Levantar los servicios:**
   ```sh
   docker-compose -f docker-compose-dev.yml up
   ```
3. **Detener los servicios:**
   ```sh
   docker-compose -f docker-compose-dev.yml down
   ```
## Cómo correr los tests
>Asegurarse de tener los servicios levantados con Docker Compose antes de correr los tests.
- Para correr todos los tests del proyecto:
   ```sh
   go test ./...
   ```
- Para correr tests específicos en un paquete:
   ```sh
   go test ./path/to/package