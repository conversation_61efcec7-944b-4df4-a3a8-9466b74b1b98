#!/bin/bash

# Script simple para escalar containers de AlPesto
# Uso: ./scale_containers.sh [preprocessors] [filters] [groupers] [aggregators] [joiners] [acción]

# Colores
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

# Valores por defecto
PREPROCESSORS=2
FILTERS=3
GROUPERS=1
AGGREGATORS=1
JOINERS=1
ACTION="generate"

# Función de ayuda
show_help() {
    echo "Uso: $0 [preprocessors] [filters] [groupers] [aggregators] [joiners] [acción]"
    echo ""
    echo "Parámetros:"
    echo "  preprocessors  - Número de preprocessors (default: 2)"
    echo "  filters        - Número de filters (default: 3)"
    echo "  groupers       - Número de groupers (default: 1)"
    echo "  aggregators    - Número de aggregators (default: 1)"
    echo "  joiners        - Número de joiners (default: 1)"
    echo "  acción         - generate|start|stop|restart (default: generate)"
    echo ""
    echo "Ejemplos:"
    echo "  $0 3 5 2 1 1 start    # 3 preprocessors, 5 filters, etc. y iniciar"
    echo "  $0 2 4              # Solo generar con 2 preprocessors, 4 filters"
    echo "  $0 help             # Mostrar esta ayuda"
}

# Parsear argumentos
if [ "$1" = "help" ] || [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_help
    exit 0
fi

if [ $# -ge 1 ] && [[ "$1" =~ ^[0-9]+$ ]]; then
    PREPROCESSORS=$1
fi

if [ $# -ge 2 ] && [[ "$2" =~ ^[0-9]+$ ]]; then
    FILTERS=$2
fi

if [ $# -ge 3 ] && [[ "$3" =~ ^[0-9]+$ ]]; then
    GROUPERS=$3
fi

if [ $# -ge 4 ] && [[ "$4" =~ ^[0-9]+$ ]]; then
    AGGREGATORS=$4
fi

if [ $# -ge 5 ] && [[ "$5" =~ ^[0-9]+$ ]]; then
    JOINERS=$5
fi

if [ $# -ge 6 ]; then
    ACTION=$6
fi

# Mostrar configuración
echo -e "${BLUE}📊 Configuración:${NC}"
echo "  Preprocessors: $PREPROCESSORS"
echo "  Filters: $FILTERS"
echo "  Groupers: $GROUPERS"
echo "  Aggregators: $AGGREGATORS"
echo "  Joiners: $JOINERS"
echo ""

# Función para generar archivos
generate_files() {
    echo -e "${YELLOW}📝 Generando archivos...${NC}"
    
    # Usar script Python si está disponible
    if command -v python3 &> /dev/null && [ -f "scale_containers.py" ]; then
        python3 scale_containers.py $PREPROCESSORS $FILTERS $GROUPERS $AGGREGATORS $JOINERS
    elif command -v python &> /dev/null && [ -f "scale_containers.py" ]; then
        python scale_containers.py $PREPROCESSORS $FILTERS $GROUPERS $AGGREGATORS $JOINERS
    else
        echo -e "${RED}❌ Error: No se encontró Python o scale_containers.py${NC}"
        echo "Instala Python o usa el método manual"
        exit 1
    fi
}

# Función para iniciar containers
start_containers() {
    echo -e "${YELLOW}🚀 Iniciando containers...${NC}"
    
    if [ ! -f "docker-compose-scaled.yml" ]; then
        echo -e "${YELLOW}⚠️  Archivo docker-compose-scaled.yml no encontrado, generando...${NC}"
        generate_files
    fi
    
    echo -e "${YELLOW}🔨 Construyendo imágenes...${NC}"
    docker-compose -f docker-compose-scaled.yml build
    
    if [ $? -eq 0 ]; then
        echo -e "${YELLOW}📦 Iniciando servicios...${NC}"
        docker-compose -f docker-compose-scaled.yml up -d
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ Containers iniciados exitosamente!${NC}"
            echo -e "${BLUE}📋 Para ver logs: docker-compose -f docker-compose-scaled.yml logs -f${NC}"
            echo -e "${BLUE}🛑 Para detener: docker-compose -f docker-compose-scaled.yml down${NC}"
            
            # Iniciar cliente si existe
            if [ -f "docker/client/docker-compose-dev.yml" ]; then
                echo -e "${YELLOW}👤 Iniciando cliente...${NC}"
                docker-compose -f docker/client/docker-compose-dev.yml up -d
            fi
        else
            echo -e "${RED}❌ Error al iniciar containers${NC}"
            exit 1
        fi
    else
        echo -e "${RED}❌ Error al construir imágenes${NC}"
        exit 1
    fi
}

# Función para detener containers
stop_containers() {
    echo -e "${YELLOW}🛑 Deteniendo containers...${NC}"
    
    # Detener cliente
    if [ -f "docker/client/docker-compose-dev.yml" ]; then
        docker-compose -f docker/client/docker-compose-dev.yml down
    fi
    
    # Detener servicios principales
    if [ -f "docker-compose-scaled.yml" ]; then
        docker-compose -f docker-compose-scaled.yml down
    fi
    
    # Detener servicios originales por si acaso
    if [ -f "docker-compose-dev.yml" ]; then
        docker-compose -f docker-compose-dev.yml down
    fi
    
    echo -e "${GREEN}✅ Containers detenidos exitosamente!${NC}"
}

# Ejecutar acción
case $ACTION in
    "generate")
        generate_files
        ;;
    "start")
        generate_files
        start_containers
        ;;
    "stop")
        stop_containers
        ;;
    "restart")
        stop_containers
        sleep 2
        generate_files
        start_containers
        ;;
    *)
        echo -e "${RED}❌ Acción desconocida: $ACTION${NC}"
        echo "Acciones válidas: generate, start, stop, restart"
        exit 1
        ;;
esac
