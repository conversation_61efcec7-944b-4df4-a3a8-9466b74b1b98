# 🚀 Escalado Simple de Containers - AlPesto

Scripts simples para escalar la cantidad de containers de cada servicio manteniendo la estructura actual de la carpeta docker.

## 📋 Uso Básico

### Python (Recomendado)
```bash
# Generar archivos con configuración por defecto (2,3,1,1,1)
python scale_containers.py

# Especificar cantidad de cada servicio
python scale_containers.py [preprocessors] [filters] [groupers] [aggregators] [joiners]

# Ejemplos
python scale_containers.py 3 5 2 1 1    # 3 preprocessors, 5 filters, etc.
python scale_containers.py 4 6          # 4 preprocessors, 6 filters, resto por defecto
```

### PowerShell (Windows)
```powershell
# Generar y iniciar con configuración por defecto
.\scale_containers.ps1 -Action start

# Especificar cantidades
.\scale_containers.ps1 -Preprocessors 3 -Filters 5 -Action start

# Solo generar archivos
.\scale_containers.ps1 -Preprocessors 4 -Filters 6

# Detener containers
.\scale_containers.ps1 -Action stop
```

### Bash (Linux/Mac)
```bash
# Generar y iniciar
./scale_containers.sh 3 5 2 1 1 start

# Solo generar
./scale_containers.sh 4 6

# Detener
./scale_containers.sh 0 0 0 0 0 stop
```

## 🎯 Parámetros

| Posición | Servicio | Default | Descripción |
|----------|----------|---------|-------------|
| 1 | Preprocessors | 2 | Procesan datos de entrada |
| 2 | Filters | 3 | Filtran por año, tiempo y monto |
| 3 | Groupers | 1 | Agrupan por diferentes criterios |
| 4 | Aggregators | 1 | Calculan métricas finales |
| 5 | Joiners | 1 | Combinan datos de fuentes |

## 📁 Archivos Generados

- `docker-compose-scaled.yml` - Archivo principal escalado
- `docker/filters/filters.compose.yml` - Configuración de filtros
- `docker/groupers/groupers.compose.yml` - Configuración de groupers
- `docker/aggregators/aggregators.compose.yml` - Configuración de aggregators
- `docker/joiners/joiners.compose.yml` - Configuración de joiners

## 🚀 Comandos Docker

### Iniciar containers escalados
```bash
docker-compose -f docker-compose-scaled.yml up -d
```

### Ver logs
```bash
docker-compose -f docker-compose-scaled.yml logs -f
```

### Detener containers
```bash
docker-compose -f docker-compose-scaled.yml down
```

### Ver estado
```bash
docker-compose -f docker-compose-scaled.yml ps
```

## 💡 Ejemplos Prácticos

### Desarrollo (mínimo)
```bash
python scale_containers.py 1 1 1 1 1
```

### Testing
```bash
python scale_containers.py 2 3 1 1 1
```

### Producción
```bash
python scale_containers.py 4 8 2 2 1
```

### Alta carga
```bash
python scale_containers.py 6 12 3 3 2
```

## 🔧 Workflow Completo

1. **Generar configuración escalada:**
   ```bash
   python scale_containers.py 3 5 2 1 1
   ```

2. **Iniciar containers:**
   ```bash
   docker-compose -f docker-compose-scaled.yml up -d
   ```

3. **Iniciar cliente (opcional):**
   ```bash
   docker-compose -f docker/client/docker-compose-dev.yml up -d
   ```

4. **Monitorear:**
   ```bash
   docker-compose -f docker-compose-scaled.yml logs -f
   ```

5. **Detener todo:**
   ```bash
   docker-compose -f docker-compose-scaled.yml down
   docker-compose -f docker/client/docker-compose-dev.yml down
   ```

## ⚠️ Notas Importantes

- Los scripts mantienen la estructura actual de la carpeta `docker/`
- Se generan archivos nuevos, no se modifican los originales
- El script Python no requiere dependencias externas
- Los containers se nombran automáticamente: `servicio-0`, `servicio-1`, etc.
- La variable `CLUSTER_SIZE` se ajusta automáticamente para cada servicio

## 🐛 Troubleshooting

### Error: Python no encontrado
- Instala Python 3.x
- O usa los scripts bash/PowerShell que llaman al script Python

### Error: Docker no responde
```bash
# Limpiar containers existentes
docker-compose -f docker-compose-dev.yml down
docker-compose -f docker-compose-scaled.yml down
```

### Error: Puerto en uso
- Detén todos los containers antes de iniciar los nuevos
- Verifica que no haya otros servicios usando los puertos

### Error: Memoria insuficiente
- Reduce la cantidad de containers
- Aumenta la memoria disponible para Docker
