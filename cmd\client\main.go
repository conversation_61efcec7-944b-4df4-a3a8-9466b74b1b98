package main

import (
	"os"

	client_controller "tp1/internal/client"

	"github.com/op/go-logging"
)

var log = logging.MustGetLogger("log")

func main() {
	serverAddress := os.Getenv("SERVER_ADDRESS")
	datasetPath := os.Getenv("DATASET_PATH")

	clientController, err := client_controller.NewClientController(datasetPath, serverAddress, log)
	if err != nil {
		log.Errorf("action: creat_client_controller | status: fail | error: %v", err)
		return
	}

	if err := clientController.Start(); err != nil {
		log.Errorf("action: start_client_controller | status: fail | error: %v", err)
	}
}
