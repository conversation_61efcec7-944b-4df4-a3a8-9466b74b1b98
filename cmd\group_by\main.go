package main

// import (
// 	"os"
// 	groupby "tp1/internal/server/group_by"
// 	"tp1/middleware"
// 	op "tp1/pkg/operators"

// 	"github.com/op/go-logging"
// )

// var log = logging.MustGetLogger("log")
// var config_path = "tp1/internal/server/group_by/group_by_config.yaml"

// func InitConfig(log *logging.Logger) (*groupby.Grouper, *op.OperatorConfig) {
// 	groupType := os.Getenv("GROUP_TYPE")

// 	grouper, err := groupby.GroupByFactory(groupType)
// 	if err != nil {
// 		log.Debugf("action: init_grouper | status: fail | error %v", err)
// 		return nil, nil
// 	}
// 	config, err := op.NewOperatorConfig(groupType, config_path)
// 	if err != nil {
// 		log.Errorf("action: init_grouper | status: fail | error: %v", err)
// 		return nil, nil
// 	}

// 	log.Debugf("action: init_grouper | status: success | type: %v", groupType)
// 	return grouper, config
// }

// func main() {
// 	middleware.Init()
// 	defer middleware.Close()

// 	grouper, config := InitConfig(log)
// 	if grouper == nil {
// 		return
// 	}

// 	operator := groupby.NewGroupOperator(grouper, config, log)
// 	err := operator.Start()

// 	if err != nil {
// 		log.Errorf("action: start_gouper_node | status: failed | error: %v", err)
// 	}
// }
