package join

import (
	"fmt"
	"strconv"
	op "tp1/pkg/operators"
	t "tp1/pkg/protocol/table_types"
)

// Join represents the configuration for a join operation.
type Join struct {
	// Key column to perform the join on
	keyColumn string
	// identifier of the smaller table, the one that is fully stored before joining
	smallTableType t.TableType
	// identifier of the larger table
	bigTableType t.TableType
	// Shard ID where the node will operate
	shardId int
	// Indicates if the small table has been fully received
	smallTableDone bool
	// storage for the small table keys (hash map for quick lookup)
	smallTableKeys map[string]struct{}
	// storage for the big table rows that are waiting to be joined
	bigTableRows [][]string
}

// JoinFactory creates a Join configuration based on the join type and shard ID.
func JoinFactory(joinType string, shardId string) (*Join, error) {
	id, err := strconv.Atoi(shardId)
	if err != nil {
		return nil, err
	}

	switch joinType {
	case op.JOIN_TYPE_MENU_COUNT, op.JOIN_TYPE_MENU_PROFIT:
		return &Join{
			keyColumn:      op.ITEM_COLUMN,
			shardId:        id,
			smallTableType: t.TableType(t.MENU),
			bigTableType:   t.TableType(t.TRANSACTION_ITEMS),
			smallTableDone: false,
			smallTableKeys: map[string]struct{}{},
			bigTableRows:   [][]string{},
		}, nil
	case op.JOIN_TYPE_STORE:
		return &Join{
			keyColumn:      op.STORE_COLUMN,
			shardId:        id,
			smallTableType: t.TableType(t.STORES),
			bigTableType:   t.TableType(t.TRANSACTIONS),
			smallTableDone: false,
			smallTableKeys: map[string]struct{}{},
			bigTableRows:   [][]string{},
		}, nil
	case op.JOIN_TYPE_USER:
		return &Join{
			keyColumn:      op.USER_COLUMN,
			shardId:        id,
			smallTableType: t.TableType(t.USERS),
			bigTableType:   t.TableType(t.TRANSACTIONS),
			smallTableDone: false,
			smallTableKeys: map[string]struct{}{},
			bigTableRows:   [][]string{},
		}, nil
	default:
		return nil, fmt.Errorf("invalid join type: %v", joinType)
	}
}

// Apply processes incoming rows based on their table type and performs the join operation accordingly.
func (j *Join) Apply(tableType t.TableType, row [][]string, columns []string) ([][]string, error) {
	var result [][]string
	if j.smallTableDone && tableType == j.smallTableType {
		// Already received small table, cannot receive it again
		return nil, fmt.Errorf("already received full small table of type %v. Shouldn't receive another batch", tableType)

	} else if j.smallTableDone && tableType == j.bigTableType {
		// received big table and already have full small table, can join and send/throw away non-joined in stream
		joined, _, err := j.joinRow(row, columns)
		if err != nil {
			return nil, err
		}

		result = joined

	} else if tableType == j.smallTableType {
		// received the small table, store it and see if it joins with what I have of the big table (send what matches, keep the rest)
		keyColumnIndex := op.GetColumnIndex(columns, j.keyColumn)
		if keyColumnIndex == -1 {
			return nil, fmt.Errorf("key column not found")
		}
		j.storeSmallTable(row, keyColumnIndex)
		joined, err := j.joinStored(columns)
		if err != nil {
			return nil, err
		}

		result = joined

	} else if tableType == j.bigTableType {
		// received the big table, try to join with what I have of the small table and keep the rest for later
		joined, notJoined, err := j.joinRow(row, columns)
		if err != nil {
			return nil, err
		}
		j.storeBigTable(notJoined)

		result = joined

	} else {
		return nil, fmt.Errorf("received unexpected table type %v", tableType)
	}

	return result, nil
}

// SetSmallTableDone marks that the small table has been fully received.
func (j *Join) SetSmallTableDone() {
	j.bigTableRows = [][]string{} // todo lo que llego hasta ahora no matcheo, no me sirve
	j.smallTableDone = true
}

// storeSmallTable stores the key column of the small table for later joining.
func (j *Join) storeSmallTable(rows [][]string, keyIndex int) {
	for _, row := range rows {
		if keyIndex < len(row) {
			key := row[keyIndex]
			if key == "" {
				continue
			}
			j.smallTableKeys[key] = struct{}{}
		}
	}
}

// storeBigTable stores the rows of the big table for later joining.
func (j *Join) storeBigTable(rows [][]string) {
	j.bigTableRows = append(j.bigTableRows, rows...)
}

// joinStored performs the join operation using what already has been stored.
// Returns the joined rows and updates the stored big table rows with those that did not join.
func (j *Join) joinStored(columns []string) ([][]string, error) {
	joined, notJoined, err := j.joinRow(j.bigTableRows, columns)
	if err != nil {
		return nil, err
	}
	j.bigTableRows = notJoined
	return joined, nil
}

// joinRow performs the join operation for the given rows of the big table, based on the keys of the small table.
// Returns the joined rows and the rows that did not join.
func (j *Join) joinRow(rows [][]string, columns []string) ([][]string, [][]string, error) {
	keyColumnIndex := op.GetColumnIndex(columns, j.keyColumn)
	if keyColumnIndex == -1 {
		return nil, nil, fmt.Errorf("key column not found")
	}

	var joinedBatch [][]string
	var notJoinedBatch [][]string
	for _, row := range rows {
		key := row[keyColumnIndex]
		if key == "" {
			continue
		}
		if _, isHere := j.smallTableKeys[key]; isHere {
			joinedBatch = append(joinedBatch, row)
		} else {
			notJoinedBatch = append(notJoinedBatch, row)
		}
	}

	return joinedBatch, notJoinedBatch, nil
}
