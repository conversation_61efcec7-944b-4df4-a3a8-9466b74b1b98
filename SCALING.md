# 📊 Guía de Escalado de Containers - AlPesto

Esta guía te ayudará a escalar los containers de AlPesto según tus necesidades de procesamiento.

## 🚀 Scripts Disponibles

### 1. Script Python (Recomendado)
`scale_containers.py` - Script completo con generación dinámica de docker-compose

### 2. Script Bash  
`scale_containers.sh` - Script más simple, compatible con sistemas Unix

## 📋 Uso Básico

### Iniciar con configuración por defecto
```bash
# Python
python scale_containers.py

# Bash (Linux/Mac)
./scale_containers.sh

# Windows (PowerShell)
python scale_containers.py
```

### Especificar cantidad de containers
```bash
# Ejemplo: 4 preprocessors, 6 filtros por año, 3 filtros por tiempo
python scale_containers.py --preprocessor 4 --filter-year 6 --filter-time 3

# Con script bash
./scale_containers.sh start --preprocessor 4 --filter-year 6 --filter-time 3
```

## 🎛️ Opciones de Escalado

| Parámetro | Descripción | Default |
|-----------|-------------|---------|
| `--preprocessor` | Número de preprocessors | 2 |
| `--filter-year` | Filtros por año | 3 |
| `--filter-time` | Filtros por tiempo | 2 |
| `--filter-amount` | Filtros por monto | 4 |
| `--grouper-product` | Groupers por producto | 1 |
| `--grouper-user-store` | Groupers por usuario-tienda | 1 |
| `--grouper-semester` | Groupers por semestre | 1 |
| `--aggregator` | Aggregators | 1 |
| `--joiner` | Joiners | 1 |

## 🎯 Configuraciones Predefinidas

### Desarrollo (Mínimo)
```bash
python scale_containers.py --preprocessor 1 --filter-year 1 --filter-time 1 --filter-amount 1
```

### Testing
```bash
python scale_containers.py --preprocessor 2 --filter-year 2 --filter-time 2 --filter-amount 2
```

### Producción
```bash
python scale_containers.py --preprocessor 4 --filter-year 6 --filter-time 4 --filter-amount 8 --grouper-product 2 --aggregator 2
```

### Alta Carga
```bash
python scale_containers.py --preprocessor 6 --filter-year 10 --filter-time 6 --filter-amount 12 --grouper-product 3 --aggregator 3
```

## 🔧 Acciones Disponibles

### Generar archivo docker-compose
```bash
python scale_containers.py --action generate --preprocessor 4
```

### Iniciar containers
```bash
python scale_containers.py --action start --preprocessor 4
```

### Reiniciar containers
```bash
python scale_containers.py --action restart --preprocessor 4
```

### Detener containers
```bash
python scale_containers.py --action stop
```

## 🛠️ Opciones Adicionales

### No construir imágenes (usar existentes)
```bash
python scale_containers.py --no-build
```

### Ejecutar en primer plano (ver logs)
```bash
python scale_containers.py --foreground
```

## 📁 Archivos Generados

- `docker-compose-scaled.yml` - Archivo docker-compose generado con la configuración escalada
- `scaling-config.yml` - Configuración personalizada (opcional)

## 🔍 Monitoreo

### Ver logs de todos los containers
```bash
docker-compose -f docker-compose-scaled.yml logs -f
```

### Ver logs de un servicio específico
```bash
docker-compose -f docker-compose-scaled.yml logs -f preprocessor-0
```

### Ver estado de containers
```bash
docker-compose -f docker-compose-scaled.yml ps
```

## 📊 Recomendaciones de Escalado

### Por Volumen de Datos

| Volumen | Preprocessor | Filter Year | Filter Time | Filter Amount |
|---------|--------------|-------------|-------------|---------------|
| Pequeño (< 1GB) | 1-2 | 1-2 | 1 | 1-2 |
| Mediano (1-10GB) | 2-4 | 3-6 | 2-3 | 4-6 |
| Grande (10-100GB) | 4-8 | 6-12 | 4-6 | 8-16 |
| Muy Grande (> 100GB) | 8+ | 12+ | 6+ | 16+ |

### Por Recursos del Sistema

| RAM Disponible | CPU Cores | Configuración Recomendada |
|----------------|-----------|---------------------------|
| 8GB | 4 cores | development |
| 16GB | 8 cores | testing |
| 32GB | 16 cores | production |
| 64GB+ | 32+ cores | high-load |

## 🚨 Troubleshooting

### Error: Puerto ya en uso
```bash
# Detener containers existentes
docker-compose -f docker-compose-dev.yml down
docker-compose -f docker-compose-scaled.yml down
```

### Error: Memoria insuficiente
- Reducir el número de containers
- Aumentar memoria disponible para Docker
- Usar configuración `development`

### Error: Dependencias no resueltas
```bash
# Reconstruir imágenes
python scale_containers.py --action restart
```

## 📝 Ejemplos Prácticos

### Escenario 1: Desarrollo Local
```bash
# Configuración mínima para desarrollo
python scale_containers.py --preprocessor 1 --filter-year 1 --filter-time 1 --filter-amount 1 --action start
```

### Escenario 2: Testing de Performance
```bash
# Configuración media para testing
python scale_containers.py --preprocessor 3 --filter-year 4 --filter-time 3 --filter-amount 5 --action start
```

### Escenario 3: Producción
```bash
# Configuración robusta para producción
python scale_containers.py --preprocessor 6 --filter-year 8 --filter-time 5 --filter-amount 10 --grouper-product 2 --aggregator 2 --action start
```

## 🔄 Migración desde Scripts Anteriores

Si estabas usando `run_containers.sh`:
```bash
# Antes
./run_containers.sh

# Ahora
python scale_containers.py --action start
```

Si estabas usando `down_containers.sh`:
```bash
# Antes  
./down_containers.sh

# Ahora
python scale_containers.py --action stop
```
