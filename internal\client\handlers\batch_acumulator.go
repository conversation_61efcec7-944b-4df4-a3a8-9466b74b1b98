package handlers

import (
	"fmt"
	"strings"
	client_server_protocol "tp1/pkg/protocol/client_server_protocol"
	t "tp1/pkg/protocol/table_types"
)

const (
	MAXPACKETSIZE = 8 * 1024 // 8KB
	MAX_ROWS      = 50
)

// BatchAcumulator represents a batch of lines for any table.
type BatchAcumulator struct {
	TableType t.TableType
	ClientId  string
	Rows      [][]string
	RowsCount int
}

// NewBatchAcumulator creates a new TableBatch with the specified parameters.
func NewBatchAcumulator(tableType t.TableType, clientId string, maxBetLines int) *BatchAcumulator {
	return &BatchAcumulator{
		TableType: tableType,
		ClientId:  clientId,
		Rows:      make([][]string, 0),
	}
}

// AddRow returns true if the row was added successfully, false otherwise.
func (b *BatchAcumulator) AddRow(row string) bool {
	if b.RowsCount >= MAX_ROWS {
		return false
	}
	splittedRow := strings.Split(row, ",")
	b.Rows = append(b.Rows, splittedRow)
	b.RowsCount++
	return true
}

// GetBatchMessage returns a Batch Message
func (b *BatchAcumulator) GetBatchMessage() *client_server_protocol.BatchMessage {
	return &client_server_protocol.BatchMessage{
		TableType: b.TableType,
		ClientId:  b.ClientId,
		Rows:      b.Rows,
	}
}

// String returns a string with the metadata of the batch.
func (b *BatchAcumulator) String() string {
	return fmt.Sprintf("TableBatch{type: %d, clientId: %s, rowsCount: %d}", b.TableType, b.ClientId, b.RowsCount)
}
