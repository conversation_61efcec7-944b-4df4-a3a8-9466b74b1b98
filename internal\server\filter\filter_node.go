package filter

import (
	"fmt"
	"tp1/middleware"
	"tp1/pkg/eot_handler"
	is_protocol "tp1/pkg/protocol/internal_server_protocol"
	"tp1/pkg/protocol/table_types"

	"github.com/op/go-logging"
)

// Filter operator that applies the filter indicated by its configuration
type FilterOperator struct {
	filter             *Filter
	logger             *logging.Logger
	producer           *middleware.MessageMiddlewareExchange
	clusterProducer    *middleware.MessageMiddlewareExchange
	consumer           *middleware.MessageMiddlewareExchange
	clusterConsumer    *middleware.MessageMiddlewareExchange
	config             *FilterConfig
	allMessagesChannel chan []byte

	eotHandler *eot_handler.EOTHandler

	clusterSize     int
	clusterRouteKey []string
}

// NewFilterOperator creates a new FilterOperator with the given configuration
func NewFilterOperator(filter *Filter, logger *logging.Logger, config *FilterConfig, clusterSize int) *FilterOperator {
	conn := middleware.GetConnection()
	ch, err := conn.Channel()
	if err != nil {
		return nil
	}
	clusterRouteKey := config.GetClusterKey()
	logger.Infof("Mi cluster route key es: ", clusterRouteKey)

	// consumes from pre processor -> both batches & end of table
	consumer, err := middleware.NewExchangeConsumer(config.ListenExchange, config.GetRouteKeysListen(), "direct", true, ch, config.QueueListen)
	if err != nil {
		return nil
	}
	// produces to next node -> both batches & end of table
	producer, err := middleware.NewExchangeProducer(config.SendExchange, config.GetRouteKeysSend(), "direct", true, ch)
	if err != nil {
		return nil
	}
	// consumes from cluter when another filter node receives end of table
	clusterConsumer, err := middleware.NewExchangeConsumer(config.ClusterEndExchange, clusterRouteKey, "fanout", false, ch, "")
	if err != nil {
		return nil
	}
	// produces to cluster when end of table is received
	clusterProducer, err := middleware.NewExchangeProducer(config.ClusterEndExchange, clusterRouteKey, "fanout", false, ch)
	if err != nil {
		return nil
	}

	f := &FilterOperator{
		filter:             filter,
		logger:             logger,
		producer:           producer,
		clusterProducer:    clusterProducer,
		consumer:           consumer,
		clusterConsumer:    clusterConsumer,
		config:             config,
		allMessagesChannel: make(chan []byte),
		clusterSize:        clusterSize,
		clusterRouteKey:    clusterRouteKey,
	}

	f.eotHandler = eot_handler.NewEOTHandler(clusterSize, f.sendToNextNode, f.sendToClusterNode, logger)

	return f
}

func (f *FilterOperator) Start() error {
	done := make(chan struct{})

	// Start consuming cluster messages
	go f.clusterConsumer.StartConsuming(f.clusterConsumer, f.filterNodeCallback)

	// Start consuming incoming messages
	go f.consumer.StartConsuming(f.consumer, f.filterNodeCallback)

	f.mainChannelListener()
	<-done
	return nil
}

func (f *FilterOperator) filterNodeCallback(consumeChannel middleware.ConsumeChannel, done chan error) {
	for d := range *consumeChannel {
		d.Ack(false)
		f.allMessagesChannel <- d.Body
	}
	done <- nil
}

func (f *FilterOperator) mainChannelListener() {
	for msg := range f.allMessagesChannel {
		err := f.handleMessage(msg)
		if err != nil {
			f.logger.Errorf("Error processing message: %v", err)
			continue
		}
	}
}

func (f *FilterOperator) Send(producer *middleware.MessageMiddlewareExchange, msg []byte) error {
	sendErr := producer.Send(producer, msg)
	if sendErr != 0 {
		f.logger.Error("Error sending message: ", sendErr)
		return fmt.Errorf("error sending message: %v", sendErr)
	}
	return nil
}

func (f FilterOperator) handleMessage(msgBytes []byte) error {
	opCode, err := is_protocol.GetServerOpCode(msgBytes)
	if err != nil {
		return err
	}

	switch opCode {
	case is_protocol.BATCH:
		return f.handleBatchMessage(msgBytes)

	case is_protocol.END_OF_TABLE, is_protocol.EOT_RECEIVED, is_protocol.EOT_ACK:
		return f.eotHandler.Handle(opCode, msgBytes)
	default: // Unknown OpCode
		f.logger.Error("action: read_msg | status: fail | error: Unkown OpCode")
	}
	return nil
}

// sendToNextNode builds and sends a new message to the next node in the pipeline.
func (f FilterOperator) sendToNextNode(formerRouteKeys []string, newMsg any, opCode is_protocol.ServerOpCode) error {
	newRouteKeys, err := f.config.GetSpecificRouteKeysSend(formerRouteKeys)
	if err != nil {
		return err
	}
	result, err := is_protocol.BuildServerMessage(newMsg, opCode, newRouteKeys)
	if err != nil {
		return err
	}

	return f.Send(f.producer, result)
}

// sendToClusterNode builds and sends a new message to teh rest of the nodes in the cluster.
func (f FilterOperator) sendToClusterNode(newMsg any, opCode is_protocol.ServerOpCode) error {
	result, err := is_protocol.BuildServerMessage(newMsg, opCode, f.clusterRouteKey)
	if err != nil {
		return err
	}
	return f.Send(f.clusterProducer, result)
}

// handleBatchMessage handles BACTH messages, sending them to the next node in pipeline.
func (f FilterOperator) handleBatchMessage(msgBytes []byte) error {
	batchMsg, err := is_protocol.NewServerMessage[is_protocol.BatchMessage](msgBytes)
	if err != nil {
		return err
	}

	filteredRows := f.filter.Apply(batchMsg.Rows, batchMsg.CSVHeaders)
	if len(filteredRows) == 0 {
		return nil
	}

	tableType := batchMsg.TableType
	if f.config.OperatorType == "amount" {
		tableType = table_types.RESULTS_Q1
	}

	resMsg := is_protocol.BatchMessage{
		TableType:  tableType,
		CSVHeaders: batchMsg.CSVHeaders,
		Rows:       filteredRows,
	}

	formerRouteKeys, err := is_protocol.GetRouteKeys(msgBytes)
	if err != nil {
		return err
	}

	return f.sendToNextNode(formerRouteKeys, resMsg, is_protocol.BATCH)
}
