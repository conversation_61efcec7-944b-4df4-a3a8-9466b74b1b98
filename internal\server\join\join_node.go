package join

// import (
// 	"fmt"
// 	is_protocol "tp1/pkg/protocol/internal_server_protocol"
// 	"tp1/middleware"
// 	op "tp1/pkg/operators"

// 	"github.com/op/go-logging"
// )

// type JoinOperator struct {
// 	joiner   *Join
// 	logger   *logging.Logger
// 	producer *middleware.MessageMiddlewareExchange
// 	consumer *middleware.MessageMiddlewareExchange
// 	config   *op.OperatorConfig
// }

// func NewJoinOperator(joiner *Join, config *op.OperatorConfig, shardId string, logger *logging.Logger) *JoinOperator {
// 	conn := middleware.GetConnection()
// 	ch, err := conn.Channel()
// 	if err != nil {
// 		return nil
// 	}
// 	consumer, err := middleware.NewExchangeConsumers(config.ListenExchange, config.RouteKeysListen, "direct", ch)
// 	if err != nil {
// 		return nil
// 	}

// 	producer, err := middleware.NewExchangeProducer(config.SendExchange[0], config.RouteKeysSend, "direct", ch)
// 	if err != nil {
// 		return nil
// 	}

// 	return &JoinOperator{
// 		joiner:   joiner,
// 		logger:   logger,
// 		producer: producer,
// 		consumer: consumer,
// 		config:   config,
// 	}
// }

// func (j *JoinOperator) Start() error {
// 	done := make(chan struct{})
// 	go j.consumer.StartConsuming(j.consumer, j.joinNodeCallback)

// 	<-done
// 	return nil
// }

// func (j *JoinOperator) joinNodeCallback(consumeChannel middleware.ConsumeChannel, done chan error) {
// 	for d := range *consumeChannel {
// 		err := d.Ack(false)

// 		if err != nil {
// 			j.logger.Error("Error acknowledging message: %v", err)
// 		}

// 		var serverMsg is_protocol.ServerMessage
// 		err = is_protocol.Decode(d.Body, &serverMsg)
// 		if err != nil {
// 			j.logger.Error("Error decoding message: %v", err)
// 		}

// 		result, err := j.processMessage(serverMsg)
// 		if err != nil {
// 			j.logger.Error("Error processing message: %v", err)
// 			continue
// 		} else if result == nil {
// 			continue
// 		}

// 		msg, _ := is_protocol.Encode(result)

// 		err2 := j.producer.Send(j.producer, msg)
// 		if err2 != 0 {
// 			j.logger.Error("Error sending message: ", err2)
// 		}
// 	}
// 	done <- nil
// }

// func (j *JoinOperator) processMessage(serverMsg is_protocol.ServerMessage) (*inprotocol.ServerMessage, error) {
// 	var result inprotocol.ServerMessage

// 	switch serverMsg.OpCode {
// 	case inprotocol.BATCHES:
// 		var batchMsg inprotocol.BatchMessage

// 		err := inprotocol.Decode(serverMsg.Payload, &batchMsg)
// 		if err != nil {
// 			j.logger.Errorf("action: read_msg | status: fail | error: %v", err)
// 			return nil, err
// 		}

// 		joinedRows, err := j.joiner.Apply(batchMsg.TableType, batchMsg.Rows, batchMsg.Columns)
// 		if err != nil || joinedRows == nil {
// 			return nil, nil
// 		}

// 		resMsg := inprotocol.BatchMessage{
// 			TableType: batchMsg.TableType,
// 			Columns:   batchMsg.Columns,
// 			Rows:      joinedRows,
// 		}

// 		batchMsgPayload, err := inprotocol.Encode(resMsg)
// 		if err != nil {
// 			return nil, err
// 		}

// 		result = inprotocol.ServerMessage{
// 			RouteKey: j.config.RouteKeysSend,
// 			OpCode:   inprotocol.BATCHES,
// 			Payload:  batchMsgPayload,
// 		}

// 	case inprotocol.END:
// 		result = serverMsg
// 		j.logger.Infof("action: read_end_msg | status: success | detail: EndTable message")
// 		// ToDo: mandar el mensaje de fin al controller

// 	// case inprotocol.OrchestratorEndTable:
// 	// // Al join solo le interesa saber que ya terminó la tabla chica, la grande no le importa
// 	// 	j.joiner.SetSmallTableDone()

// 	default: // Unkown OpCode
// 		j.logger.Error("action: read_msg | status: fail | error: Unkown OpCode")
// 	}

// 	return &result, nil
// }
