package main

// import (
// 	"os"
// 	a "tp1/internal/server/aggregator"
// 	"tp1/middleware"
// 	op "tp1/pkg/operators"

// 	"github.com/op/go-logging"
// )

// var log = logging.MustGetLogger("log")
// var config_path = "tp1/internal/server/aggregator/aggregator_config.yaml"

// func InitConfig() (*a.Aggregate, *op.OperatorConfig) {
// 	aggregatorType := os.Getenv("AGGREGATOR_TYPE")

// 	aggregator, err := a.AggregatorFactory(aggregatorType)
// 	if err != nil {
// 		log.Errorf("action: init_aggregator | status: fail | error: %v", err)
// 		return nil, nil
// 	}
// 	config, err := op.NewOperatorConfig(aggregatorType, config_path)
// 	if err != nil {
// 		log.Errorf("action: init_aggregator | status: fail | error: %v", err)
// 		return nil, nil
// 	}

// 	log.Infof("action: init_aggregator | status: ok | aggregator_type: %s", aggregatorType)
// 	return aggregator, config
// }

// func main() {
// 	middleware.Init()
// 	defer middleware.Close()

// 	aggregator, config := InitConfig()
// 	if config == nil {
// 		return
// 	}
// 	aggregatorNode := a.NewAggregateOperator(aggregator, config, log)
// 	err := aggregatorNode.Start()
// 	if err != nil {
// 		log.Errorf("action: start_gouper_node | status: failed | error: %v", err)
// 	}
// }
