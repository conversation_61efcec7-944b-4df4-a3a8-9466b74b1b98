package aggregator

// import (
// 	inprotocol "tp1/internal/server/internal_protocol"
// 	"tp1/middleware"
// 	op "tp1/pkg/operators"

// 	"github.com/op/go-logging"
// )

// type AggregateOperator struct {
// 	aggregate *Aggregate
// 	producer  *middleware.MessageMiddlewareExchange
// 	consumer  *middleware.MessageMiddlewareExchange
// 	logger    *logging.Logger
// 	config    *op.OperatorConfig
// }

// func NewAggregateOperator(aggregate *Aggregate, config *op.OperatorConfig, logger *logging.Logger) *AggregateOperator {
// 	conn := middleware.GetConnection()
// 	ch, err := conn.Channel()
// 	if err != nil {
// 		return nil
// 	}
// 	consumer, err := middleware.NewExchangeConsumer(config.ListenExchange[0], config.RouteKeysListen, "direct", ch)
// 	if err != nil {
// 		return nil
// 	}
// 	producer, err := middleware.NewExchangeProducer(config.SendExchange[0], config.RouteKeysSend, "direct", ch)
// 	if err != nil {
// 		return nil
// 	}

// 	return &AggregateOperator{
// 		aggregate: aggregate,
// 		producer:  producer,
// 		consumer:  consumer,
// 		logger:    logger,
// 		config:    config,
// 	}
// }

// func (a *AggregateOperator) Start() error {
// 	done := make(chan struct{})
// 	go a.consumer.StartConsuming(a.consumer, a.aggregatorNodeCallback)

// 	<-done
// 	return nil
// }

// func (a *AggregateOperator) aggregatorNodeCallback(consumeChannel middleware.ConsumeChannel, done chan error) {
// 	for d := range *consumeChannel {
// 		err := d.Ack(false)

// 		if err != nil {
// 			a.logger.Error("Error acknowledging message: %v", err)
// 		}

// 		var serverMsg inprotocol.ServerMessage
// 		err = inprotocol.Decode(d.Body, &serverMsg)
// 		if err != nil {
// 			a.logger.Error("Error decoding message: %v", err)
// 		}

// 		results, err := a.processMessage(serverMsg)
// 		if err != nil {
// 			a.logger.Error("Error processing message: %v", err)
// 			continue
// 		} else if results == nil {
// 			continue
// 		}

// 		for _, result := range results {
// 			msg, _ := inprotocol.Encode(result)

// 			err2 := a.producer.Send(a.producer, msg)
// 			if err2 != 0 {
// 				a.logger.Error("Error sending message: ", err2)
// 			}
// 		}
// 	}
// 	done <- nil
// }

// func (a *AggregateOperator) processMessage(serverMsg inprotocol.ServerMessage) ([]inprotocol.ServerMessage, error) {
// 	var results []inprotocol.ServerMessage

// 	switch serverMsg.OpCode {
// 	case inprotocol.BATCHES:
// 		var batchMsg inprotocol.BatchMessage

// 		err := inprotocol.Decode(serverMsg.Payload, &batchMsg)
// 		if err != nil {
// 			a.logger.Errorf("action: read_msg | status: fail | error: %v", err)
// 			return nil, err
// 		}
// 		a.aggregate.Apply(batchMsg.Rows, batchMsg.Columns)
// 		return nil, nil // aggregate does not send message yet. Return nil to avoid empty messsages

// 	case inprotocol.END:
// 		var endMsg inprotocol.EndMessage

// 		err := inprotocol.Decode(serverMsg.Payload, &endMsg)
// 		if err != nil {
// 			a.logger.Errorf("action: read_end_msg | status: fail | error: %v", err)
// 			return nil, err
// 		}

// 		a.logger.Infof("action: read_end_msg | status: success | detail: EndTable message")
// 		aggregatedRows, columns := a.aggregate.GetAggregated()

// 		resMsg := inprotocol.BatchMessage{
// 			TableType: endMsg.TableType,
// 			Columns:   columns,
// 			Rows:      aggregatedRows,
// 		}

// 		batchMsgPayload, err := inprotocol.Encode(resMsg)
// 		if err != nil {
// 			return nil, err
// 		}

// 		result := inprotocol.ServerMessage{
// 			RouteKey: a.config.RouteKeysSend,
// 			OpCode:   inprotocol.BATCHES,
// 			Payload:  batchMsgPayload,
// 		}

// 		results = []inprotocol.ServerMessage{result, serverMsg}
// 	default: // Unknown OpCode
// 		a.logger.Error("action: read_msg | status: fail | error: Unkown OpCode")
// 	}

// 	return results, nil
// }
