package groupby

import op "tp1/pkg/operators"

const (
	FIRST_SEMESTER  = "1"
	SECOND_SEMESTER = "2"
)

func getFinalColumnsForGroup(groupType string) []string {
	switch groupType {
	case op.GROUP_BY_YMP:
		return []string{"item_id", "year", "month", "count", "profit"}
	case op.GROUP_BY_YMUS:
		return []string{"store_id", "user_id", "year", "month", "count"}
	case op.GROUP_BY_YSS:
		return []string{"store_id", "year", "semester", "TPV"}
	}
	return []string{}
}
