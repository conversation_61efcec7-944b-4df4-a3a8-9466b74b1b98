package handlers

import (
	"bufio"
	"fmt"
	"iter"
	"os"
	"strings"
	cs_protocol "tp1/pkg/protocol/client_server_protocol"
	t "tp1/pkg/protocol/table_types"
)

type TableReader struct {
	TableType t.TableType
	path      string
}

// NewTableReader returns a TableReader
func NewTableReader(path string, tableType t.TableType) (*TableReader, error) {
	return &TableReader{
		TableType: tableType,
		path:      path,
	}, nil
}

// GetTableBatches returns an iterator for the batches of a table. The max amount of rows is defined by a constant
func (t *TableReader) GetTableBatches(clientId string) iter.Seq2[*cs_protocol.BatchMessage, error] {
	return func(yield func(*cs_protocol.BatchMessage, error) bool) {

		entries, err := os.ReadDir(t.path)
		if err != nil {
			yield(nil, err)
			return
		}
		batch := NewBatchAcumulator(t.TableType, clientId, MAX_ROWS)

		for _, entry := range entries {
			if entry.IsDir() {
				continue
			}

			file, err := os.Open(t.path + "/" + entry.Name())
			if err != nil {
				yield(nil, err)
				return
			}
			defer file.Close()

			scanner := bufio.NewScanner(file)
			scanner.Scan() // Skip header

			for scanner.Scan() {
				line := scanner.Text()
				if !batch.AddRow(line) {
					if !yield(batch.GetBatchMessage(), nil) {
						return
					}
					batch = NewBatchAcumulator(t.TableType, clientId, MAX_ROWS)
					batch.AddRow(line)
				}
			}
			if err := scanner.Err(); err != nil {
				yield(nil, err)
				return
			}
		}
		// pseudo flush
		if batch.RowsCount > 0 {
			if !yield(batch.GetBatchMessage(), nil) {
				return
			}
		}
	}
}

type TableWriter struct {
	TableType t.TableType
	path      string
	file      *os.File
	writer    *bufio.Writer
}

func NewTableWriter(path string, tableType t.TableType) (*TableWriter, error) {

	file, err := os.Create(path + "/" + tableType.String() + ".csv")
	if err != nil {
		return nil, fmt.Errorf("error creating file: %v", err)
	}

	writer := bufio.NewWriter(file)
	// TODO: ESCRIBIR HEADER

	return &TableWriter{
		TableType: tableType,
		path:      path,
		file:      file,
		writer:    writer,
	}, nil
}

func (tw *TableWriter) WriteRows(rows *[][]string) error {
	if tw.writer == nil {
		return fmt.Errorf("writer is not initialized")
	}

	// Write the rows to the CSV
	for _, row := range *rows {
		line := strings.Join(row, ",") + "\n"
		if _, err := tw.writer.WriteString(line); err != nil {
			return fmt.Errorf("error writing batch: %v", err)
		}
	}
	// Flush
	if err := tw.writer.Flush(); err != nil {
		return fmt.Errorf("error flushing writer: %v", err)
	}
	return nil
}

func (tw *TableWriter) Close() error {
	if tw.writer != nil {
		if err := tw.writer.Flush(); err != nil {
			return fmt.Errorf("error flushing writer: %w", err)
		}
	}

	if tw.file != nil {
		if err := tw.file.Close(); err != nil {
			return fmt.Errorf("error closing file: %w", err)
		}
	}

	return nil
}
