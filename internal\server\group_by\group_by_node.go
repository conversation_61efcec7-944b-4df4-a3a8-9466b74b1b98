package groupby

// import (
// 	"fmt"
// 	"tp1/middleware"
// 	op "tp1/pkg/operators"

// 	"github.com/op/go-logging"

// 	is_protocol "tp1/pkg/protocol/internal_server_protocol"
// )

// // Grouper operator that receives batches of rows and applies the grouping logic, sending them to the next operator.
// type GroupOperator struct {
// 	grouper  *Grouper
// 	logger   *logging.Logger
// 	producer *middleware.MessageMiddlewareExchange
// 	consumer *middleware.MessageMiddlewareExchange
// 	config   *op.OperatorConfig
// }

// // NewGroupOperator creates a new GroupOperator with the given Grouper, logger, and route keys.
// func NewGroupOperator(grouper *Grouper, config *op.OperatorConfig, logger *logging.Logger) *GroupOperator {
// 	conn := middleware.GetConnection()
// 	ch, err := conn.Channel()
// 	if err != nil {
// 		return nil
// 	}
// 	consumer, err := middleware.NewExchangeConsumer(config.ListenExchange[0], config.RouteKeysListen, "direct", ch)
// 	if err != nil {
// 		return nil
// 	}
// 	producer, err := middleware.NewExchangeProducer(config.SendExchange[0], config.RouteKeysSend, "direct", ch)
// 	if err != nil {
// 		return nil
// 	}

// 	return &GroupOperator{
// 		grouper:  grouper,
// 		logger:   logger,
// 		producer: producer,
// 		consumer: consumer,
// 		config:   config,
// 	}
// }

// func (g *GroupOperator) Start() error {
// 	done := make(chan struct{})
// 	go g.consumer.StartConsuming(g.consumer, g.groupNodeCallback)

// 	<-done
// 	return nil
// }

// func (g *GroupOperator) groupNodeCallback(consumeChannel middleware.ConsumeChannel, done chan error) {
// 	for d := range *consumeChannel {
// 		err := d.Ack(false)

// 		if err != nil {
// 			g.logger.Error("Error acknowledging message: %v", err)
// 		}

// 		var serverMsg is_protocol.ServerMessage
// 		err = is_protocol.Decode(d.Body, &serverMsg)
// 		if err != nil {
// 			g.logger.Error("Error decoding message: %v", err)
// 		}

// 		result, err := g.processMessage(serverMsg)
// 		if err != nil {
// 			g.logger.Error("Error processing message: %v", err)
// 			continue
// 		} else if result == nil {
// 			continue
// 		}
// 		msg, _ := is_protocol.Encode(result)

// 		err2 := g.producer.Send(g.producer, msg)
// 		if err2 != 0 {
// 			g.logger.Error("Error sending message: ", err2)
// 		}

// 	}
// 	done <- nil
// }

// // processMessage processes an incoming ServerMessage based on its OpCode.
// func (g *GroupOperator) processMessage(serverMsg is_protocol.ServerMessage) (*is_protocol.ServerMessage, error) {
// 	var result is_protocol.ServerMessage

// 	switch serverMsg.OpCode {
// 	case is_protocol.BATCHES: // BatchMessage
// 		var batchMsg is_protocol.BatchMessage

// 		err := is_protocol.Decode(serverMsg.Payload, &batchMsg)
// 		if err != nil {
// 			g.logger.Errorf("action: read_msg | status: fail | error: %v", err)
// 			return nil, err
// 		}

// 		columns, grouped := g.grouper.Apply(batchMsg.Rows, batchMsg.Columns, g.logger)
// 		resMsg := is_protocol.BatchMessage{
// 			TableType: batchMsg.TableType,
// 			Columns:   columns,
// 			Rows:      grouped,
// 		}

// 		fmt.Println("Grouped rows: ", grouped, " de tabla ", batchMsg.TableType)
// 		batchMsgPayload, err := is_protocol.Encode(resMsg)
// 		if err != nil {
// 			return nil, err
// 		}

// 		result = is_protocol.ServerMessage{
// 			RouteKey: g.config.RouteKeysSend,
// 			OpCode:   is_protocol.BATCHES,
// 			Payload:  batchMsgPayload,
// 		}

// 	case is_protocol.END: // EndMessage
// 		result = serverMsg
// 		g.logger.Infof("action: read_end_msg | status: success | detail: EndTable message")
// 		// ToDo: mandar el mensaje de fin al controller

// 	default: // Unkown OpCode
// 		return nil, fmt.Errorf("unknown opcode: %d", serverMsg.OpCode)
// 	}

// 	return &result, nil
// }
