package aggregator

// import (
// 	"fmt"
// 	"sort"
// 	"strings"

// 	"strconv"

// 	op "tp1/pkg/operators"
// )

// // ToDo: que las routeKeys Send del aggregator sean los shards del join

// // Aggregate represents the configuration for an aggregation operation.
// type Aggregate struct {
// 	// keyColumns is the map that has the column names that come from previous operators as keys, and their indexes as values (doesn't have to be what comes from dataset)
// 	// Example: {"item_id": 0}
// 	innerKey map[string]int
// 	// outerKey is a map of key columns used to aggregate as values, and their indexes in the input rows as values
// 	// Example: {"month":2, "year":1}
// 	outerKey map[string]int
// 	// storeedColumns is a map of the grouped columns in memory as batches are received
// 	// Example: {"abril2024prod1": {"..row.."}, "abril2024prod2": {"..row.."}, "mayo2025prod1": {"..row.."}}
// 	storeedColumns map[string]map[string][]string
// 	// top is the number of top results to keep
// 	// Example: 3 for top 3 clients
// 	top int
// 	// finalColumns is the name of columns to return in the final result
// 	// Example: "item_id","count"
// 	finalColumns []string
// 	// aggregateColumnIndex is the column to aggregate on
// 	// Example: "count" or "profit"
// 	aggregateColumnIndex int
// }

// // AggregatorFactory returns an Aggregate instance based on the given type.
// func AggregatorFactory(aggregatorType string) (*Aggregate, error) {
// 	switch aggregatorType {
// 	// case TOP 3 clients
// 	case op.AGGREGATE_CLIENT:
// 		return &Aggregate{
// 				innerKey: map[string]int{
// 					op.USER_COLUMN: op.USER_INDEX,
// 				},
// 				outerKey: map[string]int{
// 					op.YEAR_COLUMN:  op.YEAR_INDEX,
// 					op.STORE_COLUMN: op.STORE_INDEX,
// 				},
// 				storeedColumns:       map[string]map[string][]string{},
// 				top:                  op.TOP_3,
// 				finalColumns:         []string{op.STORE_COLUMN, op.USER_COLUMN, op.YEAR_COLUMN, op.MONTH_COLUMN, op.COUNT_COLUMN},
// 				aggregateColumnIndex: 4,
// 			},
// 			nil

// 	// case TOP 1 seller product by count
// 	case op.AGGREGATE_TOP_SELLER_PROD:

// 		return &Aggregate{
// 				innerKey: map[string]int{
// 					op.ITEM_COLUMN: op.ITEM_INDEX,
// 				},
// 				outerKey: map[string]int{
// 					op.YEAR_COLUMN:  op.YEAR_INDEX,
// 					op.MONTH_COLUMN: op.MONTH_INDEX,
// 				},
// 				storeedColumns:       map[string]map[string][]string{},
// 				top:                  op.TOP_1,
// 				finalColumns:         []string{op.ITEM_COLUMN, op.YEAR_COLUMN, op.MONTH_COLUMN, op.COUNT_COLUMN},
// 				aggregateColumnIndex: 3,
// 			},
// 			nil
// 	// case TOP 1 seller product by profit. Generalizar con el anterior
// 	case op.AGGREGATE_TOP_PROFIT_PROD:

// 		return &Aggregate{
// 				innerKey: map[string]int{
// 					op.ITEM_COLUMN: op.ITEM_INDEX,
// 				},
// 				outerKey: map[string]int{
// 					op.YEAR_COLUMN:  op.YEAR_INDEX,
// 					op.MONTH_COLUMN: op.MONTH_INDEX,
// 				},
// 				storeedColumns:       map[string]map[string][]string{},
// 				top:                  op.TOP_1,
// 				finalColumns:         []string{op.ITEM_COLUMN, op.YEAR_COLUMN, op.MONTH_COLUMN, op.PROFIT_COLUMN},
// 				aggregateColumnIndex: 4,
// 			},
// 			nil

// 	// case TPV per store per year per semester
// 	case op.AGGREGATE_TPV:
// 		return &Aggregate{
// 				innerKey: map[string]int{
// 					op.STORE_COLUMN: op.STORE_INDEX,
// 				},
// 				outerKey: map[string]int{
// 					op.SEMESTER_COLUMN: op.SEMESTER_INDEX,
// 					op.YEAR_COLUMN:     op.YEAR_INDEX,
// 				},
// 				storeedColumns:       map[string]map[string][]string{},
// 				top:                  op.ALL,
// 				finalColumns:         []string{op.STORE_COLUMN, op.YEAR_COLUMN, op.SEMESTER_COLUMN, op.TPV_COLUMN},
// 				aggregateColumnIndex: 3,
// 			},
// 			nil
// 	default:
// 		return nil, fmt.Errorf("unknown aggregator type: %s", aggregatorType)
// 	}
// }

// // Apply processes the given rows and updates the aggregated results.
// func (a *Aggregate) Apply(rows [][]string, columns []string) {
// 	for _, row := range rows {
// 		// get aggregation keys
// 		outKeyValues := make([]string, 0, len(a.outerKey))
// 		for _, index := range a.outerKey {
// 			if index < len(row) {
// 				outKeyValues = append(outKeyValues, row[index])
// 			}
// 		}
// 		outerKey := strings.Join(outKeyValues, "")

// 		inKeyValues := make([]string, 0, len(a.innerKey))
// 		for _, index := range a.innerKey {
// 			if index < len(row) {
// 				inKeyValues = append(inKeyValues, row[index])
// 			}
// 		}
// 		innerKey := strings.Join(inKeyValues, "")

// 		if outerMap, found := a.storeedColumns[outerKey]; found {
// 			// Ouer key already existed, check if inner key exists

// 			if innerMap, found := outerMap[innerKey]; found {
// 				// Inner key already existed, update its values
// 				currentValue, err1 := strconv.ParseFloat(row[a.aggregateColumnIndex], 64)
// 				existingValue, err2 := strconv.ParseFloat(innerMap[a.aggregateColumnIndex], 64)

// 				if err1 != nil || err2 != nil {
// 					continue
// 				}

// 				newValue := currentValue + existingValue
// 				updatedRow := make([]string, len(innerMap))
// 				copy(updatedRow, innerMap)
// 				updatedRow[a.aggregateColumnIndex] = fmt.Sprintf("%.2f", newValue)
// 				a.storeedColumns[outerKey][innerKey] = updatedRow

// 			} else {
// 				// New inner key, just store the row as is
// 				newRow := make([]string, len(row))
// 				copy(newRow, row)
// 				a.storeedColumns[outerKey][innerKey] = newRow
// 			}

// 		} else {
// 			// New key, initialize its values. Store the row as it is, so then when another value (with same key), can be compared fully.
// 			newRow := make([]string, len(row))
// 			copy(newRow, row)
// 			a.storeedColumns[outerKey] = map[string][]string{innerKey: newRow}
// 		}
// 	}
// }

// // Agregar función helper para extraer solo las columnas necesarias
// func (a *Aggregate) extractFinalColumns(row []string, inputColumns []string) []string {
// 	result := make([]string, len(a.finalColumns))

// 	for i, columnName := range a.finalColumns {
// 		// Buscar el índice de esta columna en las columnas de entrada
// 		columnIndex := op.GetColumnIndex(inputColumns, columnName)
// 		if columnIndex != -1 && columnIndex < len(row) {
// 			result[i] = row[columnIndex]
// 		} else {
// 			result[i] = "" // O valor por defecto
// 		}
// 	}

// 	return result
// }

// // GetAggregated returns the result of the aggregation. This will return the top N results based on the aggregation logic.
// func (a *Aggregate) GetAggregated() ([][]string, []string) {
// 	var aggRows [][]string

// 	for _, innerMap := range a.storeedColumns {
// 		var candidates [][]string
// 		for _, row := range innerMap {
// 			candidates = append(candidates, row)
// 		}
// 		if len(candidates) == 0 {
// 			continue
// 		}

// 		sort.Slice(candidates, func(i, j int) bool {
// 			if a.aggregateColumnIndex >= len(candidates[i]) ||
// 				a.aggregateColumnIndex >= len(candidates[j]) {
// 				return false
// 			}

// 			val1, err1 := strconv.ParseFloat(candidates[i][a.aggregateColumnIndex], 64)
// 			val2, err2 := strconv.ParseFloat(candidates[j][a.aggregateColumnIndex], 64)

// 			if err1 != nil || err2 != nil {
// 				return false
// 			}

// 			return val1 > val2
// 		})

// 		var limit int
// 		switch a.top {
// 		case op.ALL:
// 			limit = len(candidates)
// 		default:
// 			limit = a.top
// 		}
// 		if limit > len(candidates) {
// 			limit = len(candidates)
// 		}

// 		for i := 0; i < limit; i++ {
// 			aggRows = append(aggRows, candidates[i])
// 		}

// 	}
// 	return aggRows, a.finalColumns
// }

// /*
// Productos más vendidos (nombre y cant) y productos que más ganancias han generado (nombre y monto), para cada mes en 2024 y 2025.
// 1. group-by year-month-prod devuelve "item_id","year","month","count","profit"
// groupByYearMonthProduct       | GROUP DEVUELVE COLUMNS:  [item_id year month count profit]

// aggregate-top-profit-product  | columnas recibidas:  [item_id year month count profit]
// aggregate-top-seller-product  | columnas recibidas:  [item_id year month count profit]

// Fecha de cumpleaños de los 3 clientes que han hecho más compras durante 2024 y 2025, para cada sucursal.
// 2. group by year-month-store devuelve "store_id","year","month","user_id","count"
// groupByYearMonthUser          | GROUP DEVUELVE COLUMNS:  [store_id user_id year month count]

// -> Aggregate top 3 clients by count per year-store
// (para devolver los 3 clientes que han hecho más compras por año por sucursal)

// aggregate-top-client          | columnas recibidas:  [store_id year semester TPV]

// TPV (Total Payment Value) por cada semestre en 2024 y 2025, para cada sucursal, para transacciones realizadas entre las 06:00 AM y las 11:00 PM.

// 3. group by tpv (NewGroupByYearSemesterStore) devuelve "store_id","year","semester","TPV"
// groupByYearSemesterStore      | GROUP DEVUELVE COLUMNS:  [store_id year semester TPV]

// -> Aggregate TPV per store per year per semester (year-month-semester)
// (TPV usando todas las transacciones de cada sucursal en cada semestre de cada año)

// aggregate-tpv                 | columnas recibidas:  [item_id year month count profit]

// 	"2024-abril": {
// 		"item_id1": "row1",
// 		"item_id2": "row2",
// 		"item_id3": "row3",
// 		"item_id4": "row4",
//     },
//     "2024-mayo": {
//         "item_id1": "row1",
//         "item_id2": "row2",
//         "item_id3": "row3",
//     },
// */
