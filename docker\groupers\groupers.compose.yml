x-grouper-service: &grouper-service
  build:
    context: ../../
    dockerfile: docker/groupers/group_by.Dockerfile
  depends_on:
    rabbitmq:
      condition: service_healthy
  networks:
    - app-network
  env_file: ../../.env

services:

  group-by-year-month-product:
    <<: *grouper-service
    container_name: group-by-year-month-product
    environment:
      - GROUP_TYPE=year-month-product

  group-by-year-month-user-store:
    <<: *grouper-service
    container_name: group-by-year-month-user-store
    environment:
      - GROUP_TYPE=year-month-user-store

  group-by-year-semester-store:
    <<: *grouper-service
    container_name: group-by-year-semester-store
    environment:
     - GROUP_TYPE=year-semester-store
