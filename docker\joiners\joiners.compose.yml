
x-join-service: &join-service
  build:
    context: ../../
    dockerfile: docker/joiners/join.Dockerfile
  depends_on:
    rabbitmq:
      condition: service_healthy
  networks:
    - app-network
  env_file: ../../.env

services:

  join-menu-count:
    <<: *join-service
    container_name: join-menu-count
    environment:
      - JOIN_TYPE=menu_count
      - SHARD_RANGE=1
    depends_on:
      - aggregate-top-seller-product
      
  join-menu-profit:
    <<: *join-service
    container_name: join-menu-profit
    environment:
      - JOIN_TYPE=menu_profit
      - SHARD_RANGE=1
    depends_on:
      - aggregate-top-profit-product

  join-store:
    <<: *join-service
    container_name: join-store
    environment:
      - JOIN_TYPE=store
      - SHARD_RANGE=1
    depends_on:
      - aggregate-tpv

  join-user:
    <<: *join-service
    container_name: join-user
    environment:
      - JOIN_TYPE=user
      - SHARD_RANGE=1
    depends_on:
      - aggregate-top-client