x-aggregator-service: &aggregator-service
  build:
    context: ../../
    dockerfile: docker/aggregators/aggregator.Dockerfile
  depends_on:
    rabbitmq:
      condition: service_healthy
  networks:
    - app-network
  env_file: ../../.env

services:
  aggregate-top-seller-product:
    <<: *aggregator-service
    container_name: aggregate-top-seller-product
    environment:
      - AGGREGATOR_TYPE=top_seller_product
    depends_on:
      - group-by-year-month-product

  aggregate-top-profit-product:
    <<: *aggregator-service
    container_name: aggregate-top-profit-product
    environment:
      - AGGREGATOR_TYPE=top_profit_product
    depends_on:
      - group-by-year-month-product

  aggregate-top-client:
    <<: *aggregator-service
    container_name: aggregate-top-client
    environment:
      - AGGREGATOR_TYPE=client
    depends_on:
      - group-by-year-month-user-store

  aggregate-tpv:
    <<: *aggregator-service
    container_name: aggregate-tpv
    environment:
      - AGGREGATOR_TYPE=tpv
    depends_on:
      - group-by-year-semester-store