package groupby

import (
	"fmt"
	"strconv"
	"strings"
	op "tp1/pkg/operators"

	"github.com/op/go-logging"
)

// Group is a function type that defines the signature for grouping functions.
type Group func(rows [][]string, columns []string, logger *logging.Logger) [][]string

// Grouper struct holds the grouping function, logger, and final columns after grouping.
type Grouper struct {
	// groupFunc is the function that performs the grouping operation, according to the group type.
	groupFunc Group
	// finalColumns represents the columns that will be present in the output after grouping.
	finalColumns []string
}

// GroupByFactory receives a groupType string and a logger.
// Returns a Grouper instance based on the provided groupType, the route keys the node will listen from and an error if it occurs.
func GroupByFactory(groupType string) (*Grouper, error) {
	finalColumns := getFinalColumnsForGroup(groupType)
	switch groupType {
	case op.GROUP_BY_YMP:
		return &Grouper{
				groupFunc:    NewGroupByYearMonthProduct,
				finalColumns: finalColumns,
			},
			nil
	case op.GROUP_BY_YMUS:
		return &Grouper{
				groupFunc:    NewGroupByYearMonthUserStore,
				finalColumns: finalColumns,
			},
			nil
	case op.GROUP_BY_YSS:
		return &Grouper{
				groupFunc:    NewGroupByYearSemesterStore,
				finalColumns: finalColumns,
			},
			nil
	}
	return nil, fmt.Errorf("unknown group type: %s", groupType)
}

func (g *Grouper) Apply(rows [][]string, columns []string, logger *logging.Logger) ([]string, [][]string) {

	grouped := g.groupFunc(rows, columns, logger)
	return g.finalColumns, grouped
}

// recibe de transactions items: transaction_id,item_id,quantity,unit_price,subtotal,created_at
// debe tener/devolver {"year-month-prod": "item_id,year,month,count,profit"}
func NewGroupByYearMonthProduct(rows [][]string, columns []string, logger *logging.Logger) [][]string {
	gRows := make(map[string]string)
	for _, row := range rows {
		// get necessary indexes and columns
		prodIndex := op.GetColumnIndex(columns, op.ITEM_COLUMN)
		amountIndex := op.GetColumnIndex(columns, op.FINAL_AMOUNT_COLUMN) //TODO: en realidad usa subtotal
		year, month, err := getMonthAndYear(row, columns)
		if prodIndex == -1 || amountIndex == -1 || err != nil {
			continue
		}
		prod := row[prodIndex]
		amount, err := strconv.ParseFloat(row[amountIndex], 64)
		if err != nil {
			continue
		}

		// calculate key
		key := prod + month + year

		// group by logic
		if existing, found := gRows[key]; found {
			// Key already existed, update its values
			parts := strings.Split(existing, ",")
			count, _ := strconv.Atoi(parts[3]) // should not have errors if it was instanced by us
			profit, _ := strconv.ParseFloat(parts[4], 64)

			gRows[key] = fmt.Sprintf("%s,%s,%s,%d,%.2f",
				prod, year, month, count+1, profit+amount)

		} else {
			// New key, initialize its values
			gRows[key] = fmt.Sprintf("%s,%s,%s,%d,%.2f",
				prod, year, month, 1, amount)
		}
	}

	return getRowsFromMap(gRows)
}

// recibe de transactions: transaction_id,store_id,user_id,final_amount,created_at
// debe tener/devolver {"year-month-user-store": "store_id,user_id,year,month,count"}
func NewGroupByYearMonthUserStore(rows [][]string, columns []string, logger *logging.Logger) [][]string {
	gRows := make(map[string]string)
	for _, row := range rows {
		// get necessary indexes and columns
		storeIndex := op.GetColumnIndex(columns, op.STORE_COLUMN)
		userIndex := op.GetColumnIndex(columns, op.USER_COLUMN)
		year, month, err := getMonthAndYear(row, columns)
		if storeIndex == -1 || userIndex == -1 || err != nil {
			continue
		}
		store, user := row[storeIndex], row[userIndex]

		// calculate key
		key := store + user + month + year

		// group by logic
		if existing, found := gRows[key]; found {
			// Key already existed, update its values
			parts := strings.Split(existing, ",")
			count, _ := strconv.Atoi(parts[4])

			gRows[key] = fmt.Sprintf("%s,%s,%s,%s,%d",
				store, user, year, month, count+1)

		} else {
			// New key, initialize its values
			gRows[key] = fmt.Sprintf("%s,%s,%s,%s,%d",
				store, user, year, month, 1)
		}
	}

	return getRowsFromMap(gRows)
}

// recibe de transactions: transaction_id,store_id,user_id,final_amount,created_at
// debe tener/devolver {"year-semester-store": "store_id,year,semester,TPV"}
func NewGroupByYearSemesterStore(rows [][]string, columns []string, logger *logging.Logger) [][]string {
	gRows := make(map[string]string)
	for _, row := range rows {
		// get necessary indexes and columns
		storeIndex := op.GetColumnIndex(columns, op.STORE_COLUMN)
		amountIndex := op.GetColumnIndex(columns, op.FINAL_AMOUNT_COLUMN)
		year, month, err := getMonthAndYear(row, columns)
		if storeIndex == -1 || amountIndex == -1 || err != nil {
			continue
		}

		store := row[storeIndex]
		semester := getSemester(month)
		if semester == "" {
			continue
		}

		amount, err := strconv.ParseFloat(row[amountIndex], 64)
		if err != nil {
			continue
		}

		// calculate key
		key := store + semester + year

		// group by logic
		if existing, found := gRows[key]; found {
			// Key already existed, update its values
			parts := strings.Split(existing, ",")
			tpv, _ := strconv.ParseFloat(parts[3], 64)

			gRows[key] = fmt.Sprintf("%s,%s,%s,%.2f",
				store, year, semester, tpv+amount)

		} else {
			// New key, initialize its values
			gRows[key] = fmt.Sprintf("%s,%s,%s,%.2f",
				store, year, semester, amount)
		}
	}

	return getRowsFromMap(gRows)
}

// getSemester converts a month string ("01" to "12") to its corresponding semester ("1" or "2").
func getSemester(month string) string {
	monthInt, err := strconv.Atoi(month)
	if err != nil {
		return ""
	}
	if monthInt >= 1 && monthInt <= 6 {
		return FIRST_SEMESTER
	} else if monthInt >= 7 && monthInt <= 12 {
		return SECOND_SEMESTER
	}
	return ""
}

// getMonthAndYear extracts the month and year from a date string in the format "YYYY-MM-DD".
func getMonthAndYear(row []string, columns []string) (string, string, error) {
	dateIndex := op.GetColumnIndex(columns, op.TIME_COLUMN)
	if dateIndex == -1 {
		return "", "", fmt.Errorf("created_at column not found")
	}
	parts := strings.Split(row[dateIndex], "-")
	if len(parts) < 2 {
		return "", "", fmt.Errorf("invalid date format")
	}
	return parts[0], parts[1], nil
}

// getRowsFromMap converts a map of grouped rows into a slice of string slices.
func getRowsFromMap(groupedRows map[string]string) [][]string {
	result := make([][]string, 0, len(groupedRows))
	for _, v := range groupedRows {
		result = append(result, strings.Split(v, ","))
	}
	return result
}
