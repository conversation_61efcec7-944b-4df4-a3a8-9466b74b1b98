package operators

const ROUTE_KEY_TRANSACTION = "transactions"
const ROUTE_KEY_TRANSACTION_ITEMS = "transaction_items"
const ROUTE_KEY_MENU = "menu_items"
const ROUTE_KEY_STORE = "stores"
const ROUTE_KEY_USER = "users"
const ROUTE_KEY_AGREGGATOR = "aggregator"

const USER_COLUMN = "user_id"
const YEAR_COLUMN = "year"
const MONTH_COLUMN = "month"
const STORE_COLUMN = "store_id"
const COUNT_COLUMN = "count"
const ITEM_COLUMN = "item_id"
const PROFIT_COLUMN = "profit"
const SEMESTER_COLUMN = "semester"
const TPV_COLUMN = "TPV"
const TIME_COLUMN = "created_at"
const FINAL_AMOUNT_COLUMN = "final_amount"

const STORE_INDEX = 0
const ITEM_INDEX = 0
const YEAR_INDEX = 1
const MONTH_INDEX = 2
const SEMESTER_INDEX = 2
const USER_INDEX = 3
const COUNT_INDEX = 4

const TOP_1 = 1
const TOP_3 = 3
const ALL = -1

const FILTER_BY_YEAR = "year"
const FILTER_BY_TIME = "time"
const FILTER_BY_AMOUNT = "amount"

const GROUP_BY_YMP = "year-month-product"
const GROUP_BY_YMUS = "year-month-user-store"
const GROUP_BY_YSS = "year-semester-store"

const AGGREGATE_CLIENT = "client"
const AGGREGATE_TOP_SELLER_PROD = "top_seller_product"
const AGGREGATE_TOP_PROFIT_PROD = "top_profit_product"
const AGGREGATE_TPV = "tpv"

const JOIN_TYPE_MENU_PROFIT = "menu_profit"
const JOIN_TYPE_MENU_COUNT = "menu_count"
const JOIN_TYPE_STORE = "store"
const JOIN_TYPE_USER = "user"

// GetFilterIndex returns the index of the key in the columns slice, or -1 if not found
func GetColumnIndex(columns []string, key string) int {
	for i, column := range columns {
		if column == key {
			return i
		}
	}
	return -1
}
