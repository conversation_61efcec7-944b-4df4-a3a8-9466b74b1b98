package preprocessor

import (
	"fmt"
	"tp1/middleware"
	"tp1/pkg/eot_handler"
	e "tp1/pkg/errors"
	is_protocol "tp1/pkg/protocol/internal_server_protocol"

	"github.com/op/go-logging"
)

type Preprocessor struct {
	logger          *logging.Logger
	producer        *middleware.MessageMiddlewareExchange
	consumer        *middleware.MessageMiddlewareQueue
	clusterProducer *middleware.MessageMiddlewareExchange
	clusterConsumer *middleware.MessageMiddlewareExchange

	config *PreProcessorConfig

	eotHandler *eot_handler.EOTHandler

	clusterSize        int
	clusterRouteKey    []string
	allMessagesChannel chan []byte
}

func NewPreprocessor(logger *logging.Logger, config *PreProcessorConfig, clusterSize int) (*Preprocessor, error) {
	conn := middleware.GetConnection()
	ch, err := conn.Channel()
	if err != nil {
		return nil, err
	}
	consumer, err := middleware.NewQueueConsumer(config.QueueListen, ch)
	if err != nil {
		return nil, err
	}
	producer, err := middleware.NewExchangeProducer(config.SendExchange, config.GetRouteKeysSend(), "direct", true, ch)
	if err != nil {
		return nil, err
	}

	clusterRouteKey := config.GetClusterKey()
	fmt.Println("Cluster route key:", clusterRouteKey)
	fmt.Println("Cluster exchange:", config.ClusterEndExchange)

	// consumes from cluter when another filter node receives end of table
	clusterConsumer, err := middleware.NewExchangeConsumer(config.ClusterEndExchange, clusterRouteKey, "fanout", false, ch, "")
	if err != nil {
		return nil, err
	}
	// produces to cluster when end of table is received
	clusterProducer, err := middleware.NewExchangeProducer(config.ClusterEndExchange, clusterRouteKey, "fanout", false, ch)
	if err != nil {
		return nil, err
	}

	p := &Preprocessor{
		logger:             logger,
		producer:           producer,
		consumer:           consumer,
		clusterProducer:    clusterProducer,
		clusterConsumer:    clusterConsumer,
		config:             config,
		clusterSize:        clusterSize,
		clusterRouteKey:    clusterRouteKey,
		allMessagesChannel: make(chan []byte),
	}

	p.eotHandler = eot_handler.NewEOTHandler(clusterSize, p.sendToNextNode, p.sendToClusterNode, logger)
	return p, nil
}

func (p *Preprocessor) Start() error {
	done := make(chan struct{})

	// Start consuming cluster messages
	go p.clusterConsumer.StartConsuming(p.clusterConsumer, p.preprocessorCallback)

	// Start consuming incoming messages
	go p.consumer.StartConsuming(p.consumer, p.preprocessorCallback)

	p.mainChannelListener()
	<-done
	return nil
}

func (f *Preprocessor) preprocessorCallback(consumeChannel middleware.ConsumeChannel, done chan error) {
	for d := range *consumeChannel {
		d.Ack(false)
		f.allMessagesChannel <- d.Body
	}
	done <- nil
}

func (p *Preprocessor) mainChannelListener() {
	for msg := range p.allMessagesChannel {
		err := p.handleMessage(msg)
		if err != nil {
			p.logger.Errorf("Error processing message: %v", err)
			continue
		}
	}
}

func (p *Preprocessor) handleMessage(msgBytes []byte) error {
	opCode, err := is_protocol.GetServerOpCode(msgBytes)
	if err != nil {
		return err
	}

	switch opCode {
	case is_protocol.RAW_BATCH:
		rawBatchMsg, err := is_protocol.NewServerMessage[is_protocol.RawBatchMessage](msgBytes)
		if err != nil {
			return err
		}

		batchMsg, err := processTableBatch(&rawBatchMsg)
		if err != nil {
			return err
		}

		return p.sendToNextNode([]string{batchMsg.TableType.String()}, batchMsg, is_protocol.BATCH)

	case is_protocol.END_OF_TABLE, is_protocol.EOT_RECEIVED, is_protocol.EOT_ACK:
		return p.eotHandler.Handle(opCode, msgBytes)
	default:
		return e.UnkownServerOpCodeError
	}
}

// sendToNextNode builds and sends a new message to the next node in the pipeline.
func (p *Preprocessor) sendToNextNode(formerRouteKeys []string, newMsg any, opCode is_protocol.ServerOpCode) error {
	newRouteKeys, err := p.config.GetSpecificRouteKeysSend(formerRouteKeys)
	if err != nil {
		return err
	}
	result, err := is_protocol.BuildServerMessage(newMsg, opCode, newRouteKeys)
	if err != nil {
		return err
	}

	return p.Send(p.producer, result)
}

func (p *Preprocessor) Send(producer *middleware.MessageMiddlewareExchange, msg []byte) error {
	sendErr := producer.Send(producer, msg)
	if sendErr != 0 {
		p.logger.Error("Error sending message: ", sendErr)
		return fmt.Errorf("error sending message: %v", sendErr)
	}
	return nil
}

// sendToClusterNode builds and sends a new message to teh rest of the nodes in the cluster.
func (p *Preprocessor) sendToClusterNode(newMsg any, opCode is_protocol.ServerOpCode) error {
	result, err := is_protocol.BuildServerMessage(newMsg, opCode, p.clusterRouteKey)
	if err != nil {
		return err
	}
	return p.Send(p.clusterProducer, result)
}

func processTableBatch(rawBatch *is_protocol.RawBatchMessage) (*is_protocol.BatchMessage, error) {

	csvHeaders, columns_index, err := rawBatch.TableType.Headers()
	if err != nil {
		return nil, err
	}

	var rows [][]string
	for _, row := range rawBatch.Rows {
		var newRow []string
		// not enough columns in this row
		if len(row) < len(csvHeaders) {
			continue
		}
		for _, i := range columns_index {
			newRow = append(newRow, row[i])
		}
		// not enough columns after filtering
		if len(newRow) != len(csvHeaders) {
			continue
		}
		rows = append(rows, newRow)
	}

	batch := is_protocol.BatchMessage{
		TableType:  rawBatch.TableType,
		CSVHeaders: csvHeaders,
		Rows:       rows,
	}
	return &batch, nil
}
