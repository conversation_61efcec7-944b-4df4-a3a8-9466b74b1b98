# Script PowerShell simple para escalar containers de AlPesto
# Uso: .\scale_containers.ps1 [preprocessors] [filters] [groupers] [aggregators] [joiners] [acción]

param(
    [int]$Preprocessors = 2,
    [int]$Filters = 3,
    [int]$Groupers = 1,
    [int]$Aggregators = 1,
    [int]$Joiners = 1,
    [string]$Action = "generate",
    [switch]$Help
)

function Show-Help {
    Write-Host "Uso: .\scale_containers.ps1 [opciones]" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Parámetros:" -ForegroundColor Cyan
    Write-Host "  -Preprocessors N   Número de preprocessors (default: 2)"
    Write-Host "  -Filters N         Número de filters (default: 3)"
    Write-Host "  -Groupers N        Número de groupers (default: 1)"
    Write-Host "  -Aggregators N     Número de aggregators (default: 1)"
    Write-Host "  -Joiners N         Número de joiners (default: 1)"
    Write-Host "  -Action [acción]   generate|start|stop|restart (default: generate)"
    Write-Host ""
    Write-Host "Ejemplos:" -ForegroundColor Green
    Write-Host "  .\scale_containers.ps1 -Preprocessors 3 -Filters 5 -Action start"
    Write-Host "  .\scale_containers.ps1 -Preprocessors 2 -Filters 4"
    Write-Host "  .\scale_containers.ps1 -Action stop"
}

function Write-Config {
    Write-Host "📊 Configuración:" -ForegroundColor Blue
    Write-Host "  Preprocessors: $Preprocessors"
    Write-Host "  Filters: $Filters"
    Write-Host "  Groupers: $Groupers"
    Write-Host "  Aggregators: $Aggregators"
    Write-Host "  Joiners: $Joiners"
    Write-Host ""
}

function Invoke-GenerateFiles {
    Write-Host "Generando archivos..." -ForegroundColor Yellow

    # Intentar usar Python
    $pythonCmd = $null
    if (Get-Command python3 -ErrorAction SilentlyContinue) {
        $pythonCmd = "python3"
    } elseif (Get-Command python -ErrorAction SilentlyContinue) {
        $pythonCmd = "python"
    }

    if ($pythonCmd -and (Test-Path "scale_containers.py")) {
        try {
            & $pythonCmd scale_containers.py $Preprocessors $Filters $Groupers $Aggregators $Joiners
            if ($LASTEXITCODE -eq 0) {
                Write-Host "Archivos generados exitosamente!" -ForegroundColor Green
                return $true
            } else {
                throw "Error en script Python"
            }
        }
        catch {
            Write-Host "Error al ejecutar script Python: $_" -ForegroundColor Red
            return $false
        }
    } else {
        Write-Host "Error: No se encontro Python o scale_containers.py" -ForegroundColor Red
        Write-Host "Instala Python o usa el metodo manual" -ForegroundColor Yellow
        return $false
    }
}

function Start-Containers {
    Write-Host "🚀 Iniciando containers..." -ForegroundColor Yellow
    
    if (-not (Test-Path "docker-compose-scaled.yml")) {
        Write-Host "⚠️  Archivo docker-compose-scaled.yml no encontrado, generando..." -ForegroundColor Yellow
        if (-not (Invoke-GenerateFiles)) {
            return
        }
    }
    
    try {
        Write-Host "🔨 Construyendo imágenes..." -ForegroundColor Yellow
        docker-compose -f docker-compose-scaled.yml build
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "📦 Iniciando servicios..." -ForegroundColor Yellow
            docker-compose -f docker-compose-scaled.yml up -d
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ Containers iniciados exitosamente!" -ForegroundColor Green
                Write-Host "📋 Para ver logs: docker-compose -f docker-compose-scaled.yml logs -f" -ForegroundColor Blue
                Write-Host "🛑 Para detener: docker-compose -f docker-compose-scaled.yml down" -ForegroundColor Blue
                
                # Iniciar cliente si existe
                if (Test-Path "docker/client/docker-compose-dev.yml") {
                    Write-Host "👤 Iniciando cliente..." -ForegroundColor Yellow
                    docker-compose -f docker/client/docker-compose-dev.yml up -d
                }
            } else {
                throw "Error al iniciar containers"
            }
        } else {
            throw "Error al construir imágenes"
        }
    }
    catch {
        Write-Host "❌ Error: $_" -ForegroundColor Red
        exit 1
    }
}

function Stop-Containers {
    Write-Host "🛑 Deteniendo containers..." -ForegroundColor Yellow
    
    # Detener cliente
    if (Test-Path "docker/client/docker-compose-dev.yml") {
        docker-compose -f docker/client/docker-compose-dev.yml down
    }
    
    # Detener servicios principales
    if (Test-Path "docker-compose-scaled.yml") {
        docker-compose -f docker-compose-scaled.yml down
    }
    
    # Detener servicios originales por si acaso
    if (Test-Path "docker-compose-dev.yml") {
        docker-compose -f docker-compose-dev.yml down
    }
    
    Write-Host "✅ Containers detenidos exitosamente!" -ForegroundColor Green
}

# Función principal
if ($Help) {
    Show-Help
    exit 0
}

Write-Config

switch ($Action.ToLower()) {
    "generate" {
        Invoke-GenerateFiles
    }
    "start" {
        Invoke-GenerateFiles
        Start-Containers
    }
    "stop" {
        Stop-Containers
    }
    "restart" {
        Stop-Containers
        Start-Sleep -Seconds 2
        Invoke-GenerateFiles
        Start-Containers
    }
    default {
        Write-Host "❌ Acción desconocida: $Action" -ForegroundColor Red
        Write-Host "Acciones válidas: generate, start, stop, restart" -ForegroundColor Yellow
        exit 1
    }
}
