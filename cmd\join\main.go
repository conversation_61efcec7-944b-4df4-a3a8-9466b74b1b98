package main

// import (
// 	"os"
// 	join "tp1/internal/server/join"
// 	"tp1/middleware"

// 	"github.com/op/go-logging"

// 	op "tp1/pkg/operators"
// )

// var log = logging.MustGetLogger("log")
// var config_path = "tp1/internal/server/join/join_config.yaml"

// func InitConfig() (*join.Join, *op.OperatorConfig, string) {
// 	joinType := os.Getenv("JOIN_TYPE")
// 	shardId := os.Getenv("SHARD_RANGE")

// 	joiner, err := join.JoinFactory(joinType, shardId)
// 	if err != nil {
// 		log.Errorf("action: join_factory | status: fail | error: invalid join type %v or shard %v", joinType, shardId)
// 		return nil, nil, ""
// 	}
// 	config, err := op.NewOperatorConfig(joinType, config_path)
// 	if err != nil {
// 		log.Errorf("action: join_factory | status: fail | error: %v", err)
// 		return nil, nil, ""
// 	}

// 	log.Infof("action: join_factory | status: success | join_type: %v | shard: %v", joinType, shardId)
// 	return joiner, config, shardId
// }

// func main() {
// 	middleware.Init()
// 	defer middleware.Close()

// 	// joiner, config, shardId := InitConfig()
// 	// if joiner == nil {
// 	// 	return
// 	// }

// 	// joinNode := join.NewJoinOperator(joiner, config, shardId, log)
// 	// err := joinNode.Start()
// 	// if err != nil {
// 	// 	log.Errorf("action: start_join_node | status: fail | error: %v", err)
// 	// }
// }
